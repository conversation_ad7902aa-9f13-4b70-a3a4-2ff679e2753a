{"cells": [{"cell_type": "markdown", "metadata": {"id": "resume_parser_title"}, "source": ["# Resume Parser with Transformer Models\n", "## NavTech Assignment - AI/ML Engineer Role\n", "\n", "This notebook demonstrates a comprehensive resume parser that can handle PDF, DOC, and DOCX files using various LLM providers including:\n", "- Google Gemini\n", "- OpenAI GPT\n", "- OpenRouter (Free APIs)\n", "- Local Transformer Models (BERT, RoBERTa)\n", "\n", "**Output Format**: Structured JSON matching NavTech requirements"]}, {"cell_type": "markdown", "metadata": {"id": "setup_section"}, "source": ["## 1. Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_dependencies"}, "outputs": [], "source": ["# Install required packages\n", "!pip install -q python-dotenv pydantic jsonschema\n", "!pip install -q PyPDF2 pdfplumber python-docx docx2txt\n", "!pip install -q transformers torch spacy nltk\n", "!pip install -q openai google-generativeai requests\n", "!pip install -q pandas numpy regex tqdm colorama\n", "\n", "# Download spaCy model\n", "!python -m spacy download en_core_web_sm\n", "\n", "print(\"✅ All dependencies installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import_libraries"}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "import json\n", "import logging\n", "from pathlib import Path\n", "from typing import Dict, Any, List, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"📚 Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "config_section"}, "source": ["## 2. Configuration and Schema Definition"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "define_schema"}, "outputs": [], "source": ["# Define output schema (matching NavTech requirements)\n", "from pydantic import BaseModel, Field\n", "from typing import List\n", "\n", "class Address(BaseModel):\n", "    city: str = Field(default=\"\", description=\"City name\")\n", "    state: str = Field(default=\"\", description=\"State/Province code\")\n", "    country: str = Field(default=\"\", description=\"Country code\")\n", "\n", "class Skill(BaseModel):\n", "    skill: str = Field(description=\"Skill name\")\n", "\n", "class Education(BaseModel):\n", "    name: str = Field(description=\"Institution name\")\n", "    degree: str = Field(description=\"Degree/qualification\")\n", "    from_date: str = Field(default=\" \", description=\"Start date\")\n", "    to_date: str = Field(default=\" \", description=\"End date\")\n", "\n", "class WorkExperience(BaseModel):\n", "    company: str = Field(description=\"Company name\")\n", "    title: str = Field(description=\"Job title/position\")\n", "    description: str = Field(description=\"Job description\")\n", "    from_date: str = Field(default=\" \", description=\"Start date\")\n", "    to_date: str = Field(default=\" \", description=\"End date\")\n", "\n", "class ResumeData(BaseModel):\n", "    first_name: str = Field(default=\"\", description=\"First name\")\n", "    last_name: str = Field(default=\"\", description=\"Last name\")\n", "    email: str = Field(default=\"\", description=\"Email address\")\n", "    phone: str = Field(default=\"\", description=\"Phone number\")\n", "    address: Address = Field(default_factory=Address, description=\"Address information\")\n", "    summary: str = Field(default=\"\", description=\"Professional summary\")\n", "    skills: List[Skill] = Field(default_factory=list, description=\"List of skills\")\n", "    education_history: List[Education] = Field(default_factory=list, description=\"Education history\")\n", "    work_history: List[WorkExperience] = Field(default_factory=list, description=\"Work experience\")\n", "\n", "    def to_dict(self) -> dict:\n", "        return {\n", "            \"first_name\": self.first_name,\n", "            \"last_name\": self.last_name,\n", "            \"email\": self.email,\n", "            \"phone\": self.phone,\n", "            \"address\": {\n", "                \"city\": self.address.city,\n", "                \"state\": self.address.state,\n", "                \"country\": self.address.country\n", "            },\n", "            \"summary\": self.summary,\n", "            \"skills\": [{\"skill\": skill.skill} for skill in self.skills],\n", "            \"education_history\": [\n", "                {\n", "                    \"name\": edu.name,\n", "                    \"degree\": edu.degree,\n", "                    \"from_date\": edu.from_date,\n", "                    \"to_date\": edu.to_date\n", "                }\n", "                for edu in self.education_history\n", "            ],\n", "            \"work_history\": [\n", "                {\n", "                    \"company\": work.company,\n", "                    \"title\": work.title,\n", "                    \"description\": work.description,\n", "                    \"from_date\": work.from_date,\n", "                    \"to_date\": work.to_date\n", "                }\n", "                for work in self.work_history\n", "            ]\n", "        }\n", "\n", "print(\"📋 <PERSON>hem<PERSON> defined successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "api_keys_config"}, "outputs": [], "source": ["# API Keys Configuration\n", "# Add your API keys here or use environment variables\n", "\n", "# Option 1: Set API keys directly (not recommended for production)\n", "GEMINI_API_KEY = \"\"  # Add your Gemini API key\n", "OPENAI_API_KEY = \"\"  # Add your OpenAI API key\n", "OPENROUTER_API_KEY = \"\"  # Add your OpenRouter API key\n", "\n", "# Option 2: Use Google Colab secrets (recommended)\n", "try:\n", "    from google.colab import userdata\n", "    GEMINI_API_KEY = userdata.get('GEMINI_API_KEY') if not GEMINI_API_KEY else GEMINI_API_KEY\n", "    OPENAI_API_KEY = userdata.get('OPENAI_API_KEY') if not OPENAI_API_KEY else OPENAI_API_KEY\n", "    OPENROUTER_API_KEY = userdata.get('OPENROUTER_API_KEY') if not OPENROUTER_API_KEY else OPENROUTER_API_KEY\n", "except:\n", "    pass\n", "\n", "# Extraction prompt template\n", "EXTRACTION_PROMPT_TEMPLATE = \"\"\"\n", "You are an expert resume parser. Extract the following information from the resume text and return it in the exact JSON format specified.\n", "\n", "Resume Text:\n", "{resume_text}\n", "\n", "Required JSON Format:\n", "{{\n", "    \"first_name\": \"string\",\n", "    \"last_name\": \"string\",\n", "    \"email\": \"string\", \n", "    \"phone\": \"string\",\n", "    \"address\": {{\n", "        \"city\": \"string\",\n", "        \"state\": \"string\",\n", "        \"country\": \"string\"\n", "    }},\n", "    \"summary\": \"string\",\n", "    \"skills\": [{{\"skill\": \"string\"}}],\n", "    \"education_history\": [{{\n", "        \"name\": \"string\",\n", "        \"degree\": \"string\", \n", "        \"from_date\": \"string\",\n", "        \"to_date\": \"string\"\n", "    }}],\n", "    \"work_history\": [{{\n", "        \"company\": \"string\",\n", "        \"title\": \"string\",\n", "        \"description\": \"string\",\n", "        \"from_date\": \"string\", \n", "        \"to_date\": \"string\"\n", "    }}]\n", "}}\n", "\n", "Instructions:\n", "1. Extract all personal information (name, email, phone, address)\n", "2. Create a professional summary from the resume content\n", "3. List all technical and professional skills\n", "4. Extract education history with institutions, degrees, and dates\n", "5. Extract work experience with companies, titles, descriptions, and dates\n", "6. Use \" \" (space) for missing dates\n", "7. Return ONLY the JSON object, no additional text\n", "8. Ensure all fields are present even if empty\n", "\n", "JSON Response:\"\"\"\n", "\n", "print(\"🔧 Configuration completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "file_processors_section"}, "source": ["## 3. File Processing Classes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "file_processors"}, "outputs": [], "source": ["# File processors for different formats\n", "import PyPDF2\n", "import pdfplumber\n", "import docx\n", "import docx2txt\n", "from abc import ABC, abstractmethod\n", "\n", "class BaseFileProcessor(ABC):\n", "    \"\"\"Abstract base class for file processors\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.supported_extensions = []\n", "    \n", "    @abstractmethod\n", "    def extract_text(self, file_path: str) -> str:\n", "        \"\"\"Extract text from file\"\"\"\n", "        pass\n", "    \n", "    def clean_text(self, text: str) -> str:\n", "        \"\"\"Basic text cleaning\"\"\"\n", "        if not text:\n", "            return \"\"\n", "        \n", "        # Remove excessive whitespace\n", "        text = \" \".join(text.split())\n", "        \n", "        # Remove common artifacts\n", "        text = text.replace(\"\\x00\", \"\")  # Null characters\n", "        text = text.replace(\"\\ufffd\", \"\")  # Replacement characters\n", "        \n", "        return text.strip()\n", "\n", "class PDFProcessor(BaseFileProcessor):\n", "    \"\"\"PDF file processor\"\"\"\n", "    \n", "    def __init__(self):\n", "        super().__init__()\n", "        self.supported_extensions = ['.pdf']\n", "    \n", "    def extract_text(self, file_path: str) -> str:\n", "        \"\"\"Extract text from PDF\"\"\"\n", "        # Try pdfplumber first\n", "        text = self._extract_with_pdfplumber(file_path)\n", "        \n", "        # Fallback to PyPDF2\n", "        if not text or len(text.strip()) < 50:\n", "            text = self._extract_with_pypdf2(file_path)\n", "        \n", "        return self.clean_text(text)\n", "    \n", "    def _extract_with_pdfplumber(self, file_path: str) -> str:\n", "        try:\n", "            text_parts = []\n", "            with pdfplumber.open(file_path) as pdf:\n", "                for page in pdf.pages:\n", "                    page_text = page.extract_text()\n", "                    if page_text:\n", "                        text_parts.append(page_text)\n", "            return \"\\n\".join(text_parts)\n", "        except Exception as e:\n", "            logger.error(f\"pdfplumber extraction failed: {e}\")\n", "            return \"\"\n", "    \n", "    def _extract_with_pypdf2(self, file_path: str) -> str:\n", "        try:\n", "            text_parts = []\n", "            with open(file_path, 'rb') as file:\n", "                pdf_reader = PyPDF2.PdfReader(file)\n", "                for page in pdf_reader.pages:\n", "                    page_text = page.extract_text()\n", "                    if page_text:\n", "                        text_parts.append(page_text)\n", "            return \"\\n\".join(text_parts)\n", "        except Exception as e:\n", "            logger.error(f\"PyPDF2 extraction failed: {e}\")\n", "            return \"\"\n", "\n", "class DOCXProcessor(BaseFileProcessor):\n", "    \"\"\"DOCX/DOC file processor\"\"\"\n", "    \n", "    def __init__(self):\n", "        super().__init__()\n", "        self.supported_extensions = ['.docx', '.doc']\n", "    \n", "    def extract_text(self, file_path: str) -> str:\n", "        \"\"\"Extract text from Word document\"\"\"\n", "        file_ext = Path(file_path).suffix.lower()\n", "        \n", "        if file_ext == '.docx':\n", "            text = self._extract_from_docx(file_path)\n", "        else:\n", "            # For .doc files, try docx2txt\n", "            text = self._extract_with_docx2txt(file_path)\n", "        \n", "        return self.clean_text(text)\n", "    \n", "    def _extract_from_docx(self, file_path: str) -> str:\n", "        try:\n", "            text_parts = []\n", "            doc = docx.Document(file_path)\n", "            \n", "            # Extract paragraphs\n", "            for paragraph in doc.paragraphs:\n", "                if paragraph.text.strip():\n", "                    text_parts.append(paragraph.text)\n", "            \n", "            # Extract tables\n", "            for table in doc.tables:\n", "                for row in table.rows:\n", "                    row_text = []\n", "                    for cell in row.cells:\n", "                        if cell.text.strip():\n", "                            row_text.append(cell.text.strip())\n", "                    if row_text:\n", "                        text_parts.append(\" | \".join(row_text))\n", "            \n", "            return \"\\n\".join(text_parts)\n", "        except Exception as e:\n", "            logger.error(f\"DOCX extraction failed: {e}\")\n", "            return self._extract_with_docx2txt(file_path)\n", "    \n", "    def _extract_with_docx2txt(self, file_path: str) -> str:\n", "        try:\n", "            return docx2txt.process(file_path)\n", "        except Exception as e:\n", "            logger.error(f\"docx2txt extraction failed: {e}\")\n", "            return \"\"\n", "\n", "class FileProcessorFactory:\n", "    \"\"\"Factory to create appropriate file processor\"\"\"\n", "    \n", "    @staticmethod\n", "    def create_processor(file_path: str) -> BaseFileProcessor:\n", "        file_ext = Path(file_path).suffix.lower()\n", "        \n", "        if file_ext == '.pdf':\n", "            return PDFProcessor()\n", "        elif file_ext in ['.docx', '.doc']:\n", "            return DOCXProcessor()\n", "        else:\n", "            raise ValueError(f\"Unsupported file format: {file_ext}\")\n", "\n", "print(\"📄 File processors defined successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "llm_providers_section"}, "source": ["## 4. LLM Providers"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "base_llm_provider"}, "outputs": [], "source": ["# Base LLM Provider\n", "import re\n", "\n", "class BaseLLMProvider(ABC):\n", "    \"\"\"Abstract base class for LLM providers\"\"\"\n", "    \n", "    def __init__(self, config: Dict[str, Any]):\n", "        self.config = config\n", "    \n", "    @abstractmethod\n", "    def extract_resume_data(self, resume_text: str) -> ResumeData:\n", "        \"\"\"Extract structured data from resume text\"\"\"\n", "        pass\n", "    \n", "    @abstractmethod\n", "    def is_available(self) -> bool:\n", "        \"\"\"Check if the LLM provider is available\"\"\"\n", "        pass\n", "    \n", "    def _clean_json_response(self, response_text: str) -> str:\n", "        \"\"\"Clean and extract JSON from LLM response\"\"\"\n", "        response_text = response_text.strip()\n", "        \n", "        # Remove markdown code blocks\n", "        if response_text.startswith(\"```json\"):\n", "            response_text = response_text[7:]\n", "        elif response_text.startswith(\"```\"):\n", "            response_text = response_text[3:]\n", "        \n", "        if response_text.endswith(\"```\"):\n", "            response_text = response_text[:-3]\n", "        \n", "        # Find JSON object boundaries\n", "        start_idx = response_text.find(\"{\")\n", "        end_idx = response_text.rfind(\"}\") + 1\n", "        \n", "        if start_idx != -1 and end_idx > start_idx:\n", "            response_text = response_text[start_idx:end_idx]\n", "        \n", "        return response_text.strip()\n", "    \n", "    def _parse_json_response(self, response_text: str) -> Dict[str, Any]:\n", "        \"\"\"Parse JSON response from LLM\"\"\"\n", "        try:\n", "            cleaned_text = self._clean_json_response(response_text)\n", "            return json.loads(cleaned_text)\n", "        except json.JSONDecodeError as e:\n", "            logger.error(f\"Failed to parse JSON response: {e}\")\n", "            raise ValueError(f\"Invalid JSON response from LLM: {e}\")\n", "    \n", "    def _create_resume_data_from_dict(self, data_dict: Dict[str, Any]) -> ResumeData:\n", "        \"\"\"Create ResumeData object from dictionary\"\"\"\n", "        try:\n", "            resume_data = ResumeData(\n", "                first_name=data_dict.get(\"first_name\", \"\"),\n", "                last_name=data_dict.get(\"last_name\", \"\"),\n", "                email=data_dict.get(\"email\", \"\"),\n", "                phone=data_dict.get(\"phone\", \"\"),\n", "                summary=data_dict.get(\"summary\", \"\")\n", "            )\n", "            \n", "            # Handle address\n", "            address_data = data_dict.get(\"address\", {})\n", "            if isinstance(address_data, dict):\n", "                resume_data.address = Address(\n", "                    city=address_data.get(\"city\", \"\"),\n", "                    state=address_data.get(\"state\", \"\"),\n", "                    country=address_data.get(\"country\", \"\")\n", "                )\n", "            \n", "            # Handle skills\n", "            skills_data = data_dict.get(\"skills\", [])\n", "            if isinstance(skills_data, list):\n", "                resume_data.skills = [\n", "                    Skill(skill=skill.get(\"skill\", \"\") if isinstance(skill, dict) else str(skill))\n", "                    for skill in skills_data\n", "                ]\n", "            \n", "            # Handle education\n", "            education_data = data_dict.get(\"education_history\", [])\n", "            if isinstance(education_data, list):\n", "                resume_data.education_history = [\n", "                    Education(\n", "                        name=edu.get(\"name\", \"\"),\n", "                        degree=edu.get(\"degree\", \"\"),\n", "                        from_date=edu.get(\"from_date\", \" \"),\n", "                        to_date=edu.get(\"to_date\", \" \")\n", "                    )\n", "                    for edu in education_data if isinstance(edu, dict)\n", "                ]\n", "            \n", "            # Handle work experience\n", "            work_data = data_dict.get(\"work_history\", [])\n", "            if isinstance(work_data, list):\n", "                resume_data.work_history = [\n", "                    WorkExperience(\n", "                        company=work.get(\"company\", \"\"),\n", "                        title=work.get(\"title\", \"\"),\n", "                        description=work.get(\"description\", \"\"),\n", "                        from_date=work.get(\"from_date\", \" \"),\n", "                        to_date=work.get(\"to_date\", \" \")\n", "                    )\n", "                    for work in work_data if isinstance(work, dict)\n", "                ]\n", "            \n", "            return resume_data\n", "        \n", "        except Exception as e:\n", "            logger.error(f\"Failed to create ResumeData from dict: {e}\")\n", "            raise ValueError(f\"Failed to process extracted data: {e}\")\n", "    \n", "    def _get_fallback_data(self, resume_text: str) -> ResumeData:\n", "        \"\"\"Generate fallback data when LLM extraction fails\"\"\"\n", "        logger.warning(\"Using fallback data extraction\")\n", "        \n", "        # Basic regex-based extraction\n", "        email_pattern = r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b'\n", "        emails = re.findall(email_pattern, resume_text)\n", "        email = emails[0] if emails else \"\"\n", "        \n", "        phone_pattern = r'[\\+]?[1-9]?[0-9]{7,15}'\n", "        phones = re.findall(phone_pattern, resume_text)\n", "        phone = phones[0] if phones else \"\"\n", "        \n", "        return ResumeData(\n", "            first_name=\"\",\n", "            last_name=\"\",\n", "            email=email,\n", "            phone=phone,\n", "            summary=\"Resume parsing failed. Manual review required.\"\n", "        )\n", "\n", "print(\"🤖 Base LLM provider defined successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gemini_provider"}, "outputs": [], "source": ["# Google Gemini Provider\n", "try:\n", "    import google.generativeai as genai\n", "    \n", "    class GeminiLLMProvider(BaseLLMProvider):\n", "        \"\"\"Google Gemini LLM provider\"\"\"\n", "        \n", "        def __init__(self, api_key: str = None):\n", "            super().__init__({\"model\": \"gemini-1.5-pro\", \"temperature\": 0.1})\n", "            self.api_key = api_key or GEMINI_API_KEY\n", "            self.model = None\n", "            self._initialize_model()\n", "        \n", "        def _initialize_model(self):\n", "            if not self.api_key:\n", "                logger.error(\"Gemini API key not found\")\n", "                return\n", "            \n", "            try:\n", "                genai.configure(api_key=self.api_key)\n", "                self.model = genai.GenerativeModel(model_name=\"gemini-1.5-pro\")\n", "                logger.info(\"Gemini model initialized successfully\")\n", "            except Exception as e:\n", "                logger.error(f\"Failed to initialize Gemini model: {e}\")\n", "                self.model = None\n", "        \n", "        def is_available(self) -> bool:\n", "            return self.model is not None and bool(self.api_key)\n", "        \n", "        def extract_resume_data(self, resume_text: str) -> ResumeData:\n", "            if not self.is_available():\n", "                logger.error(\"Gemini model not available\")\n", "                return self._get_fallback_data(resume_text)\n", "            \n", "            try:\n", "                prompt = EXTRACTION_PROMPT_TEMPLATE.format(resume_text=resume_text)\n", "                \n", "                response = self.model.generate_content(\n", "                    prompt,\n", "                    generation_config=genai.types.GenerationConfig(\n", "                        temperature=0.1,\n", "                        max_output_tokens=4000\n", "                    )\n", "                )\n", "                \n", "                response_text = response.text\n", "                data_dict = self._parse_json_response(response_text)\n", "                resume_data = self._create_resume_data_from_dict(data_dict)\n", "                \n", "                logger.info(\"Successfully extracted resume data using Gemini\")\n", "                return resume_data\n", "            \n", "            except Exception as e:\n", "                logger.error(f\"Gemini extraction failed: {e}\")\n", "                return self._get_fallback_data(resume_text)\n", "    \n", "    print(\"✅ Gemini provider defined successfully!\")\n", "    \n", "except ImportError:\n", "    print(\"⚠️ Google Generative AI not available\")\n", "    \n", "    class GeminiLLMProvider(BaseLLMProvider):\n", "        def __init__(self, api_key: str = None):\n", "            super().__init__({})\n", "        \n", "        def is_available(self) -> bool:\n", "            return False\n", "        \n", "        def extract_resume_data(self, resume_text: str) -> ResumeData:\n", "            return self._get_fallback_data(resume_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "openai_provider"}, "outputs": [], "source": ["# OpenAI Provider\n", "try:\n", "    import openai\n", "    \n", "    class OpenAILLMProvider(BaseLLMProvider):\n", "        \"\"\"OpenAI GPT LLM provider\"\"\"\n", "        \n", "        def __init__(self, api_key: str = None):\n", "            super().__init__({\"model\": \"gpt-3.5-turbo\", \"temperature\": 0.1})\n", "            self.api_key = api_key or OPENAI_API_KEY\n", "            self.client = None\n", "            self._initialize_client()\n", "        \n", "        def _initialize_client(self):\n", "            if not self.api_key:\n", "                logger.error(\"OpenAI API key not found\")\n", "                return\n", "            \n", "            try:\n", "                self.client = openai.OpenAI(api_key=self.api_key)\n", "                logger.info(\"OpenAI client initialized successfully\")\n", "            except Exception as e:\n", "                logger.error(f\"Failed to initialize OpenAI client: {e}\")\n", "                self.client = None\n", "        \n", "        def is_available(self) -> bool:\n", "            return self.client is not None and bool(self.api_key)\n", "        \n", "        def extract_resume_data(self, resume_text: str) -> ResumeData:\n", "            if not self.is_available():\n", "                logger.error(\"OpenAI client not available\")\n", "                return self._get_fallback_data(resume_text)\n", "            \n", "            try:\n", "                prompt = EXTRACTION_PROMPT_TEMPLATE.format(resume_text=resume_text)\n", "                \n", "                response = self.client.chat.completions.create(\n", "                    model=\"gpt-3.5-turbo\",\n", "                    messages=[\n", "                        {\"role\": \"system\", \"content\": \"You are an expert resume parser. Extract information and return only valid JSON.\"},\n", "                        {\"role\": \"user\", \"content\": prompt}\n", "                    ],\n", "                    temperature=0.1,\n", "                    max_tokens=4000\n", "                )\n", "                \n", "                response_text = response.choices[0].message.content\n", "                data_dict = self._parse_json_response(response_text)\n", "                resume_data = self._create_resume_data_from_dict(data_dict)\n", "                \n", "                logger.info(\"Successfully extracted resume data using OpenAI\")\n", "                return resume_data\n", "            \n", "            except Exception as e:\n", "                logger.error(f\"OpenAI extraction failed: {e}\")\n", "                return self._get_fallback_data(resume_text)\n", "    \n", "    print(\"✅ OpenAI provider defined successfully!\")\n", "    \n", "except ImportError:\n", "    print(\"⚠️ OpenAI not available\")\n", "    \n", "    class OpenAILLMProvider(BaseLLMProvider):\n", "        def __init__(self, api_key: str = None):\n", "            super().__init__({})\n", "        \n", "        def is_available(self) -> bool:\n", "            return False\n", "        \n", "        def extract_resume_data(self, resume_text: str) -> ResumeData:\n", "            return self._get_fallback_data(resume_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "transformer_provider"}, "outputs": [], "source": ["# Local Transformer Provider\n", "try:\n", "    from transformers import pipeline\n", "    import torch\n", "    \n", "    class TransformerLLMProvider(BaseLLMProvider):\n", "        \"\"\"Local Transformer models provider\"\"\"\n", "        \n", "        def __init__(self):\n", "            super().__init__({})\n", "            self.ner_pipeline = None\n", "            self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "            self._initialize_models()\n", "        \n", "        def _initialize_models(self):\n", "            try:\n", "                self.ner_pipeline = pipeline(\n", "                    \"ner\",\n", "                    model=\"dbmdz/bert-large-cased-finetuned-conll03-english\",\n", "                    aggregation_strategy=\"simple\",\n", "                    device=0 if self.device == \"cuda\" else -1\n", "                )\n", "                logger.info(f\"Transformer models initialized on {self.device}\")\n", "            except Exception as e:\n", "                logger.error(f\"Failed to initialize transformer models: {e}\")\n", "                self.ner_pipeline = None\n", "        \n", "        def is_available(self) -> bool:\n", "            return self.ner_pipeline is not None\n", "        \n", "        def extract_resume_data(self, resume_text: str) -> ResumeData:\n", "            if not self.is_available():\n", "                logger.error(\"Transformer models not available\")\n", "                return self._get_fallback_data(resume_text)\n", "            \n", "            try:\n", "                # Extract entities using NER\n", "                entities = self._extract_entities(resume_text)\n", "                \n", "                # Extract structured information\n", "                resume_data = ResumeData()\n", "                \n", "                # Extract personal information\n", "                resume_data.first_name, resume_data.last_name = self._extract_name(resume_text, entities)\n", "                resume_data.email = self._extract_email(resume_text)\n", "                resume_data.phone = self._extract_phone(resume_text)\n", "                resume_data.address = self._extract_address(resume_text, entities)\n", "                \n", "                # Extract professional information\n", "                resume_data.summary = self._extract_summary(resume_text)\n", "                resume_data.skills = self._extract_skills(resume_text)\n", "                resume_data.education_history = self._extract_education(resume_text, entities)\n", "                resume_data.work_history = self._extract_work_experience(resume_text, entities)\n", "                \n", "                logger.info(\"Successfully extracted resume data using transformers\")\n", "                return resume_data\n", "            \n", "            except Exception as e:\n", "                logger.error(f\"Transformer extraction failed: {e}\")\n", "                return self._get_fallback_data(resume_text)\n", "        \n", "        def _extract_entities(self, text: str) -> List[Dict]:\n", "            try:\n", "                # Split text into chunks for long resumes\n", "                max_length = 512\n", "                chunks = [text[i:i+max_length] for i in range(0, len(text), max_length)]\n", "                \n", "                all_entities = []\n", "                for chunk in chunks:\n", "                    entities = self.ner_pipeline(chunk)\n", "                    all_entities.extend(entities)\n", "                \n", "                return all_entities\n", "            except Exception as e:\n", "                logger.error(f\"Entity extraction failed: {e}\")\n", "                return []\n", "        \n", "        def _extract_name(self, text: str, entities: List[Dict]) -> tuple:\n", "            # Look for PERSON entities\n", "            person_entities = [e for e in entities if e.get(\"entity_group\") == \"PER\"]\n", "            \n", "            if person_entities:\n", "                full_name = person_entities[0][\"word\"].strip()\n", "                name_parts = full_name.split()\n", "                \n", "                if len(name_parts) >= 2:\n", "                    return name_parts[0], \" \".join(name_parts[1:])\n", "                elif len(name_parts) == 1:\n", "                    return name_parts[0], \"\"\n", "            \n", "            return \"\", \"\"\n", "        \n", "        def _extract_email(self, text: str) -> str:\n", "            email_pattern = r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b'\n", "            emails = re.findall(email_pattern, text)\n", "            return emails[0] if emails else \"\"\n", "        \n", "        def _extract_phone(self, text: str) -> str:\n", "            patterns = [\n", "                r'\\+\\d{1,3}[-\\.\\s]?\\d{3,4}[-\\.\\s]?\\d{3,4}[-\\.\\s]?\\d{3,4}',\n", "                r'\\(\\d{3}\\)[-\\.\\s]?\\d{3}[-\\.\\s]?\\d{4}',\n", "                r'\\d{3}[-\\.\\s]?\\d{3}[-\\.\\s]?\\d{4}',\n", "                r'\\+\\d{10,15}'\n", "            ]\n", "            \n", "            for pattern in patterns:\n", "                matches = re.findall(pattern, text)\n", "                if matches:\n", "                    return matches[0]\n", "            \n", "            return \"\"\n", "        \n", "        def _extract_address(self, text: str, entities: List[Dict]) -> Address:\n", "            locations = [e for e in entities if e.get(\"entity_group\") in [\"LOC\", \"GPE\"]]\n", "            \n", "            city, state, country = \"\", \"\", \"\"\n", "            \n", "            if locations:\n", "                if len(locations) >= 1:\n", "                    city = locations[-1][\"word\"]\n", "                if len(locations) >= 2:\n", "                    state = locations[-2][\"word\"]\n", "                if len(locations) >= 3:\n", "                    country = locations[-3][\"word\"]\n", "            \n", "            return Address(city=city, state=state, country=country)\n", "        \n", "        def _extract_summary(self, text: str) -> str:\n", "            summary_keywords = [\"summary\", \"profile\", \"objective\", \"about\"]\n", "            lines = text.split('\\n')\n", "            \n", "            for i, line in enumerate(lines):\n", "                if any(keyword in line.lower() for keyword in summary_keywords):\n", "                    summary_lines = []\n", "                    for j in range(i+1, min(i+5, len(lines))):\n", "                        if lines[j].strip() and not lines[j].isupper():\n", "                            summary_lines.append(lines[j].strip())\n", "                        else:\n", "                            break\n", "                    \n", "                    if summary_lines:\n", "                        return \" \".join(summary_lines)\n", "            \n", "            # Fallback: take first paragraph\n", "            for line in lines[:10]:\n", "                if len(line.split()) > 10 and not line.isupper():\n", "                    return line.strip()\n", "            \n", "            return \"Professional summary not found in resume.\"\n", "        \n", "        def _extract_skills(self, text: str) -> List[Skill]:\n", "            skill_keywords = [\n", "                \"python\", \"java\", \"javascript\", \"typescript\", \"react\", \"angular\", \"vue\",\n", "                \"node.js\", \"express\", \"django\", \"flask\", \"spring\", \"html\", \"css\", \"sql\",\n", "                \"mongodb\", \"postgresql\", \"mysql\", \"aws\", \"azure\", \"docker\", \"kubernetes\",\n", "                \"git\", \"linux\", \"windows\", \"machine learning\", \"ai\", \"data science\"\n", "            ]\n", "            \n", "            found_skills = []\n", "            text_lower = text.lower()\n", "            \n", "            for skill in skill_keywords:\n", "                if skill in text_lower:\n", "                    found_skills.append(Skill(skill=skill))\n", "            \n", "            return found_skills[:20]  # Limit to 20 skills\n", "        \n", "        def _extract_education(self, text: str, entities: List[Dict]) -> List[Education]:\n", "            # Simple education extraction\n", "            education_keywords = [\"education\", \"university\", \"college\", \"degree\"]\n", "            lines = text.split('\\n')\n", "            \n", "            education_list = []\n", "            for line in lines:\n", "                if any(keyword in line.lower() for keyword in education_keywords):\n", "                    if len(line.split()) > 2:  # Avoid single words\n", "                        education_list.append(Education(\n", "                            name=line.strip(),\n", "                            degree=\"\",\n", "                            from_date=\" \",\n", "                            to_date=\" \"\n", "                        ))\n", "            \n", "            return education_list[:3]  # Limit to 3 entries\n", "        \n", "        def _extract_work_experience(self, text: str, entities: List[Dict]) -> List[WorkExperience]:\n", "            # Simple work experience extraction\n", "            work_keywords = [\"experience\", \"work\", \"company\", \"position\"]\n", "            lines = text.split('\\n')\n", "            \n", "            work_list = []\n", "            for line in lines:\n", "                if any(keyword in line.lower() for keyword in work_keywords):\n", "                    if len(line.split()) > 3:  # Avoid single words\n", "                        work_list.append(WorkExperience(\n", "                            company=line.strip(),\n", "                            title=\"\",\n", "                            description=\"\",\n", "                            from_date=\" \",\n", "                            to_date=\" \"\n", "                        ))\n", "            \n", "            return work_list[:3]  # Limit to 3 entries\n", "    \n", "    print(\"✅ Transformer provider defined successfully!\")\n", "    \n", "except ImportError:\n", "    print(\"⚠️ Transformers not available\")\n", "    \n", "    class TransformerLLMProvider(BaseLLMProvider):\n", "        def __init__(self):\n", "            super().__init__({})\n", "        \n", "        def is_available(self) -> bool:\n", "            return False\n", "        \n", "        def extract_resume_data(self, resume_text: str) -> ResumeData:\n", "            return self._get_fallback_data(resume_text)"]}, {"cell_type": "markdown", "metadata": {"id": "main_parser_section"}, "source": ["## 5. Main Resume Parser Class"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "main_parser"}, "outputs": [], "source": ["# Main Resume Parser\n", "class ResumeParser:\n", "    \"\"\"Main resume parser class\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.providers = self._initialize_providers()\n", "    \n", "    def _initialize_providers(self) -> dict:\n", "        \"\"\"Initialize all LLM providers\"\"\"\n", "        providers = {}\n", "        \n", "        try:\n", "            providers['gemini'] = GeminiLLMProvider()\n", "            print(f\"Gemini provider: {'✅ Available' if providers['gemini'].is_available() else '❌ Not available'}\")\n", "        except Exception as e:\n", "            print(f\"❌ Failed to initialize Gemini provider: {e}\")\n", "        \n", "        try:\n", "            providers['openai'] = OpenAILLMProvider()\n", "            print(f\"OpenAI provider: {'✅ Available' if providers['openai'].is_available() else '❌ Not available'}\")\n", "        except Exception as e:\n", "            print(f\"❌ Failed to initialize OpenAI provider: {e}\")\n", "        \n", "        try:\n", "            providers['transformer'] = TransformerLLMProvider()\n", "            print(f\"Transformer provider: {'✅ Available' if providers['transformer'].is_available() else '❌ Not available'}\")\n", "        except Exception as e:\n", "            print(f\"❌ Failed to initialize Transformer provider: {e}\")\n", "        \n", "        return providers\n", "    \n", "    def parse_resume(self, file_path: str, llm_provider: str = \"gemini\") -> ResumeData:\n", "        \"\"\"Parse resume file and extract structured data\"\"\"\n", "        print(f\"🔍 Starting resume parsing: {file_path} with {llm_provider}\")\n", "        \n", "        # Extract text from file\n", "        try:\n", "            processor = FileProcessorFactory.create_processor(file_path)\n", "            resume_text = processor.extract_text(file_path)\n", "            print(f\"📄 Extracted {len(resume_text)} characters from resume\")\n", "        except Exception as e:\n", "            print(f\"❌ Failed to extract text from file: {e}\")\n", "            raise\n", "        \n", "        # Get LLM provider\n", "        if llm_provider not in self.providers:\n", "            raise ValueError(f\"Unknown LLM provider: {llm_provider}\")\n", "        \n", "        provider = self.providers[llm_provider]\n", "        if not provider.is_available():\n", "            print(f\"⚠️ {llm_provider} provider not available, falling back to transformer\")\n", "            provider = self.providers.get('transformer')\n", "            if not provider or not provider.is_available():\n", "                raise ValueError(\"No LLM providers available\")\n", "        \n", "        # Extract structured data\n", "        try:\n", "            resume_data = provider.extract_resume_data(resume_text)\n", "            print(\"✅ Successfully extracted resume data\")\n", "            return resume_data\n", "        except Exception as e:\n", "            print(f\"❌ Failed to extract resume data: {e}\")\n", "            raise\n", "    \n", "    def get_available_providers(self) -> list:\n", "        \"\"\"Get list of available LLM providers\"\"\"\n", "        available = []\n", "        for name, provider in self.providers.items():\n", "            if provider and provider.is_available():\n", "                available.append(name)\n", "        return available\n", "\n", "# Initialize the parser\n", "print(\"🚀 Initializing Resume Parser...\")\n", "resume_parser = ResumeParser()\n", "print(f\"\\n📋 Available providers: {resume_parser.get_available_providers()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "demo_section"}, "source": ["## 6. <PERSON><PERSON> and Us<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_demo"}, "outputs": [], "source": ["# File Upload Demo\n", "from google.colab import files\n", "\n", "def upload_and_parse_resume(llm_provider=\"gemini\"):\n", "    \"\"\"Upload and parse a resume file\"\"\"\n", "    print(\"📁 Please upload your resume file (PDF, DOC, or DOCX)\")\n", "    \n", "    # Upload file\n", "    uploaded = files.upload()\n", "    \n", "    if not uploaded:\n", "        print(\"❌ No file uploaded\")\n", "        return None\n", "    \n", "    # Get the uploaded file\n", "    filename = list(uploaded.keys())[0]\n", "    print(f\"📄 Processing file: {filename}\")\n", "    \n", "    try:\n", "        # Parse the resume\n", "        resume_data = resume_parser.parse_resume(filename, llm_provider)\n", "        \n", "        # Convert to JSON and display\n", "        output_json = resume_data.to_dict()\n", "        \n", "        print(\"\\n📋 Extracted Resume Data:\")\n", "        print(\"=\" * 50)\n", "        print(json.dumps(output_json, indent=2, ensure_ascii=False))\n", "        \n", "        return output_json\n", "    \n", "    except Exception as e:\n", "        print(f\"❌ Error processing resume: {e}\")\n", "        return None\n", "    \n", "    finally:\n", "        # Clean up uploaded file\n", "        try:\n", "            os.remove(filename)\n", "        except:\n", "            pass\n", "\n", "print(\"📤 Upload function ready! Call upload_and_parse_resume() to start.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "example_usage"}, "outputs": [], "source": ["# Example with sample resume text\n", "sample_resume_text = \"\"\"\n", "<PERSON>\n", "Software Engineer\n", "Email: <EMAIL>\n", "Phone: ******-123-4567\n", "Address: San Francisco, CA, USA\n", "\n", "SUMMARY\n", "Experienced software engineer with 5+ years in full-stack development. \n", "Proficient in Python, JavaScript, and cloud technologies.\n", "\n", "SKILLS\n", "• Python, JavaScript, TypeScript\n", "• React, Node.js, Django\n", "• <PERSON><PERSON>, <PERSON><PERSON>, Kubernetes\n", "• SQL, MongoDB\n", "\n", "EDUCATION\n", "Bachelor of Science in Computer Science\n", "Stanford University\n", "2015 - 2019\n", "\n", "WORK EXPERIENCE\n", "Senior Software Engineer\n", "Tech Corp Inc.\n", "2021 - Present\n", "Led development of microservices architecture and improved system performance by 40%.\n", "\"\"\"\n", "\n", "def parse_resume_text(resume_text: str, llm_provider=\"gemini\"):\n", "    \"\"\"Parse resume from text input\"\"\"\n", "    if not resume_text.strip():\n", "        print(\"❌ Please provide resume text\")\n", "        return None\n", "    \n", "    try:\n", "        # Get the provider\n", "        if llm_provider not in resume_parser.providers:\n", "            print(f\"❌ Unknown LLM provider: {llm_provider}\")\n", "            return None\n", "        \n", "        provider = resume_parser.providers[llm_provider]\n", "        if not provider.is_available():\n", "            print(f\"⚠️ {llm_provider} provider not available, falling back to transformer\")\n", "            provider = resume_parser.providers.get('transformer')\n", "            if not provider or not provider.is_available():\n", "                print(\"❌ No LLM providers available\")\n", "                return None\n", "        \n", "        print(f\"🔍 Processing resume text with {llm_provider}...\")\n", "        \n", "        # Extract structured data\n", "        resume_data = provider.extract_resume_data(resume_text)\n", "        \n", "        # Convert to JSON and display\n", "        output_json = resume_data.to_dict()\n", "        \n", "        print(\"\\n📋 Extracted Resume Data:\")\n", "        print(\"=\" * 50)\n", "        print(json.dumps(output_json, indent=2, ensure_ascii=False))\n", "        \n", "        return output_json\n", "    \n", "    except Exception as e:\n", "        print(f\"❌ Error processing resume: {e}\")\n", "        return None\n", "\n", "print(\"🧪 Running example with sample resume...\")\n", "available_providers = resume_parser.get_available_providers()\n", "if available_providers:\n", "    provider_to_use = available_providers[0]\n", "    print(f\"\\n🤖 Using provider: {provider_to_use}\")\n", "    result = parse_resume_text(sample_resume_text, provider_to_use)\n", "else:\n", "    print(\"❌ No providers available for demo\")"]}, {"cell_type": "markdown", "metadata": {"id": "instructions_section"}, "source": ["## 7. Instructions for Use\n", "\n", "### Option 1: Upload File\n", "```python\n", "# Upload and parse a resume file\n", "result = upload_and_parse_resume(llm_provider=\"gemini\")\n", "```\n", "\n", "### Option 2: Text Input\n", "```python\n", "# Parse resume from text\n", "resume_text = \"Your resume text here...\"\n", "result = parse_resume_text(resume_text, llm_provider=\"gemini\")\n", "```\n", "\n", "### Available LLM Providers:\n", "- **gemini**: Google Gemini (requires API key)\n", "- **openai**: OpenAI GPT (requires API key)  \n", "- **transformer**: Local BERT models (no API key needed)\n", "\n", "### Setting API Keys:\n", "1. **Google Colab Secrets** (Recommended):\n", "   - Go to the key icon in the left sidebar\n", "   - Add secrets: `GEMINI_API_KEY`, `OPENAI_API_KEY`\n", "\n", "2. **Direct Assignment**:\n", "   ```python\n", "   GEMINI_API_KEY = \"your-api-key-here\"\n", "   OPENAI_API_KEY = \"your-api-key-here\"\n", "   ```\n", "\n", "### Output Format:\n", "The parser returns a JSON object matching the NavTech requirements with all required fields:\n", "- Personal information (name, email, phone, address)\n", "- Professional summary\n", "- Skills list\n", "- Education history\n", "- Work experience\n", "\n", "---\n", "\n", "**NavTech Assignment Completed** ✅\n", "\n", "This notebook demonstrates a comprehensive resume parser using transformer models and multiple LLM providers, capable of handling PDF, DOC, and DOCX files with structured JSON output."]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}