{% extends "base.html" %}

{% block title %}Resume Analysis Results - NavTech Resume Parser{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Header -->
        <div class="card mb-4">
            <div class="card-header" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h4 class="mb-1 text-white">
                            <i class="fas fa-check-circle me-2"></i> Analysis Complete
                        </h4>
                        <small class="text-white opacity-75">Resume successfully processed and analyzed</small>
                    </div>
                    <div class="text-end">
                        <div class="badge bg-white text-success px-3 py-2">
                            <i class="fas fa-bolt me-1"></i> Success
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="feature-icon" style="width: 50px; height: 50px;">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-1 fw-bold">{{ filename }}</h6>
                                <div class="d-flex align-items-center gap-2">
                                    <span class="text-muted">Processed with</span>
                                    <span class="badge bg-primary">{{ provider.replace('_', ' ').title() }}</span>
                                    <span class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Verified
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end mt-3 mt-md-0">
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i> Parse Another Resume
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Personal Information -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i> Personal Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-sm-6">
                        <div class="info-item">
                            <label class="text-muted small fw-bold">FIRST NAME</label>
                            <div class="fw-bold text-primary">{{ result.first_name or 'Not found' }}</div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="info-item">
                            <label class="text-muted small fw-bold">LAST NAME</label>
                            <div class="fw-bold text-primary">{{ result.last_name or 'Not found' }}</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="info-item">
                            <label class="text-muted small fw-bold">EMAIL ADDRESS</label>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-envelope text-info me-2"></i>
                                <span class="fw-bold">{{ result.email or 'Not found' }}</span>
                                {% if result.email %}
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ result.email }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="info-item">
                            <label class="text-muted small fw-bold">PHONE NUMBER</label>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-phone text-info me-2"></i>
                                <span class="fw-bold">{{ result.phone or 'Not found' }}</span>
                                {% if result.phone %}
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ result.phone }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% if result.address %}
                    <div class="col-12">
                        <div class="info-item">
                            <label class="text-muted small fw-bold">ADDRESS</label>
                            <div class="d-flex align-items-start">
                                <i class="fas fa-map-marker-alt text-info me-2 mt-1"></i>
                                <div>
                                    {% if result.address.street %}{{ result.address.street }}<br>{% endif %}
                                    {% if result.address.city %}{{ result.address.city }}{% endif %}
                                    {% if result.address.state %}, {{ result.address.state }}{% endif %}
                                    {% if result.address.zip_code %} {{ result.address.zip_code }}{% endif %}
                                    {% if result.address.country %}<br>{{ result.address.country }}{% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Summary -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-text me-2"></i> Professional Summary
                </h5>
            </div>
            <div class="card-body">
                {% if result.summary %}
                <div class="summary-content">
                    <p class="mb-0 lh-lg">{{ result.summary }}</p>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-file-text text-muted fs-1 mb-3"></i>
                    <p class="text-muted fst-italic mb-0">No professional summary found in the resume</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Skills -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> Skills 
                    <span class="badge bg-secondary">{{ result.skills|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if result.skills %}
                <div class="row">
                    {% for skill in result.skills %}
                    <div class="col-md-6 mb-2">
                        <span class="badge bg-light text-dark">{{ skill.name }}</span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted fst-italic">No skills found</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Education -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap"></i> Education 
                    <span class="badge bg-secondary">{{ result.education_history|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if result.education_history %}
                {% for education in result.education_history %}
                <div class="mb-3 {% if not loop.last %}border-bottom pb-3{% endif %}">
                    <h6 class="text-primary">{{ education.degree or 'Degree' }}</h6>
                    <p class="mb-1"><strong>Institution:</strong> {{ education.institution or 'Not specified' }}</p>
                    {% if education.graduation_year %}
                    <p class="mb-0"><strong>Year:</strong> {{ education.graduation_year }}</p>
                    {% endif %}
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted fst-italic">No education information found</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Work Experience -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase"></i> Work Experience 
                    <span class="badge bg-secondary">{{ result.work_history|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if result.work_history %}
                {% for work in result.work_history %}
                <div class="mb-4 {% if not loop.last %}border-bottom pb-4{% endif %}">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-primary">{{ work.position or 'Position' }}</h6>
                            <p class="mb-1"><strong>Company:</strong> {{ work.company or 'Not specified' }}</p>
                            {% if work.description %}
                            <p class="text-muted">{{ work.description }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-4 text-md-end">
                            {% if work.start_date or work.end_date %}
                            <small class="text-muted">
                                {{ work.start_date or 'Start' }} - {{ work.end_date or 'End' }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% else %}
                <p class="text-muted fst-italic">No work experience found</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- JSON Output -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-code"></i> JSON Output
                    <button class="btn btn-sm btn-outline-secondary float-end" onclick="copyToClipboard()">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </h5>
            </div>
            <div class="card-body">
                <pre class="json-output" id="jsonOutput">{{ json_output }}</pre>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="{{ url_for('index') }}" class="btn btn-primary me-2">
            <i class="fas fa-upload"></i> Parse Another Resume
        </a>
        <a href="{{ url_for('demo') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-play"></i> Try Demo
        </a>
        <button class="btn btn-outline-success" onclick="downloadJSON()">
            <i class="fas fa-download"></i> Download JSON
        </button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function copyToClipboard(text) {
        // For JSON output (when no text parameter)
        if (!text) {
            const jsonOutput = document.getElementById('jsonOutput');
            text = jsonOutput.textContent;
        }

        // Use modern clipboard API if available
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                showCopyFeedback('Copied to clipboard!');
            }).catch(() => {
                fallbackCopyToClipboard(text);
            });
        } else {
            fallbackCopyToClipboard(text);
        }
    }

    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showCopyFeedback('Copied to clipboard!');
        } catch (err) {
            showCopyFeedback('Copy failed. Please select and copy manually.');
        }

        document.body.removeChild(textArea);
    }

    function showCopyFeedback(message) {
        // Remove existing feedback
        const existingFeedback = document.querySelector('.copy-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // Create new feedback
        const feedback = document.createElement('div');
        feedback.className = 'copy-feedback';
        feedback.innerHTML = `<i class="fas fa-check me-2"></i>${message}`;
        document.body.appendChild(feedback);

        // Show feedback
        setTimeout(() => feedback.classList.add('show'), 100);

        // Hide feedback after 3 seconds
        setTimeout(() => {
            feedback.classList.remove('show');
            setTimeout(() => feedback.remove(), 300);
        }, 3000);
    }

    function downloadJSON() {
        const jsonData = {{ json_output|safe }};
        const dataStr = JSON.stringify(jsonData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = '{{ filename }}_parsed.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showCopyFeedback('JSON file downloaded!');
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scrolling to anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add hover effects to info items
        document.querySelectorAll('.info-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% endblock %}
