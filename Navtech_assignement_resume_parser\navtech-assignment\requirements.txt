# Core dependencies
python-dotenv==1.0.0
pydantic==2.5.0
jsonschema==4.20.0

# File processing
PyPDF2==3.0.1
pdfplumber==0.10.0
python-docx==1.1.0
docx2txt==0.8

# NLP and ML
transformers==4.36.0
torch==2.1.0
spacy==3.7.0
nltk==3.8.1

# Enhanced document processing (Method 1: Smart Processing)
pillow==10.1.0
pdf2image==1.16.3
pytesseract==0.3.10
opencv-python==4.8.1.78

# LayoutLM dependencies (Method 2: AI-Powered Processing) - Optional
# Uncomment the following lines to enable LayoutLM functionality:
# torch==2.1.0
# torchvision==0.16.0
# transformers==4.36.0
# Note: LayoutLM requires significant additional dependencies and setup

# LLM providers
openai==1.3.0
google-generativeai==0.3.0
requests==2.31.0

# Data processing
pandas==2.1.0
numpy==1.24.0
regex==2023.10.3

# Utilities
tqdm==4.66.0
colorama==0.4.6
click==8.1.7

# Development
pytest==7.4.0
black==23.11.0
flake8==6.1.0

# Jupyter (for Colab compatibility)
jupyter==1.0.0
ipywidgets==8.1.0
