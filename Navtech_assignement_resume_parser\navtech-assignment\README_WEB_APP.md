# 🚀 NavTech Resume Parser - Complete Testing Suite

This project implements an AI-powered resume parser with **real LLM integration** and provides **three different ways** for recruiters to test the system.

## 🎯 Assignment Highlights

- ✅ **Real LLM Integration**: Actual API calls to Gemini, OpenAI, OpenRouter
- ✅ **Structured Prompting**: Dynamic prompt construction with resume text
- ✅ **Multiple Testing Methods**: Web UI, CLI, Google Colab
- ✅ **Production Ready**: Error handling, validation, fallbacks
- ✅ **No Hardcoding**: All responses generated by real AI models

## 🌟 Three Ways to Test (For Recruiters)

### 1. 🌐 Web Interface (Recommended)

**Start the Flask web application:**
```bash
python app.py
```

**Then open:** http://localhost:5000

**Features:**
- 📁 Upload resume files via drag & drop
- 🤖 Select LLM provider (Gemini, OpenAI, OpenRouter, Transformer)
- 🔑 Add custom API keys
- 📊 View parsed results in beautiful UI
- 💾 Download JSON output
- 🎯 Demo with sample resume

**Pages Available:**
- `/` - Main upload interface
- `/demo` - Demo with sample resume
- `/providers` - Check provider status
- `/api/parse` - API endpoint for programmatic access

### 2. 💻 Command Line Interface

```bash
# Parse with Gemini (requires API key)
python src/main.py --file sample_resumes/sample_resume.txt --llm gemini

# Parse with local transformer models (no API key required)
python src/main.py --file sample_resumes/sample_resume.txt --llm transformer

# Save output to specific file
python src/main.py --file resume.pdf --llm gemini --output results.json

# List available providers
python src/main.py --list-providers
```

### 3. 📓 Google Colab Notebook

1. Upload `NavTech_Resume_Parser_Colab.ipynb` to Google Colab
2. Run cells to install dependencies
3. Upload your resume file or use sample
4. Select LLM provider and add API key
5. View parsed results and download JSON

## 🔧 Quick Setup

### Prerequisites
- Python 3.8+
- pip package manager

### Installation
```bash
# 1. Navigate to project directory
cd navtech-assignment

# 2. Install dependencies
pip install -r requirements.txt
pip install flask

# 3. (Optional) Set up API keys in .env file
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 4. Start web application
python app.py
```

## 🤖 LLM Providers

### Google Gemini ⭐ (Recommended)
- **API Key Required**: Yes
- **Free Tier**: Generous limits
- **Setup**: [Get API key](https://makersuite.google.com/app/apikey)
- **Model**: gemini-1.5-pro

### OpenRouter 🆓 (Free Option)
- **API Key Required**: Yes
- **Free Tier**: Free credits available
- **Setup**: [Get API key](https://openrouter.ai/)
- **Models**: Multiple models available

### OpenAI GPT
- **API Key Required**: Yes
- **Free Tier**: Limited
- **Setup**: [Get API key](https://platform.openai.com/api-keys)
- **Model**: gpt-3.5-turbo

### Local Transformers 🔧
- **API Key Required**: No
- **Free Tier**: Completely free
- **Setup**: Models downloaded automatically
- **Model**: BERT-based NER models

## 📊 Real LLM Parsing Process

### 1. Prompt Construction (4,700+ characters)
```python
prompt = f"""
You are an expert resume parser. Extract the following information from the resume text and return it in the exact JSON format specified.

Resume Text:
{resume_text}

Required JSON Format:
{{
    "first_name": "string",
    "last_name": "string",
    "email": "string",
    ...
}}

Instructions:
1. Extract all personal information (name, email, phone, address)
2. Create a professional summary from the resume content
3. List all technical and professional skills
4. Extract education history with institutions, degrees, and dates
5. Extract work experience with companies, titles, descriptions, and dates
6. Use " " (space) for missing dates
7. Return ONLY the JSON object, no additional text
8. Ensure all fields are present even if empty

JSON Response:
"""
```

### 2. Real API Call
```python
response = model.generate_content(
    prompt,
    generation_config={
        "temperature": 0.1,
        "max_output_tokens": 4000,
        "top_p": 0.8
    }
)
```

### 3. Response Processing
- Parse JSON response from LLM
- Validate against schema
- Handle errors and fallbacks
- Return structured ResumeData object

## 📄 Sample Output

```json
{
  "first_name": "Vijay",
  "last_name": "Pagare",
  "email": "<EMAIL>",
  "phone": "+91889XXXXX28",
  "address": {
    "city": "Thane",
    "state": "MH",
    "country": "India"
  },
  "summary": "A frontend-leaning software engineer with 4.5+ years of experience in building and maintaining high-quality SaaS products and web applications.",
  "skills": [
    {"skill": "JavaScript"},
    {"skill": "TypeScript"},
    {"skill": "React"},
    {"skill": "NextJS"},
    {"skill": "Angular 2+"}
  ],
  "education_history": [{
    "name": "Rajiv Gandhi Institute of Technology",
    "degree": "Bachelor of Engineering - Computers",
    "from_date": "2015",
    "to_date": "2019"
  }],
  "work_history": [{
    "company": "PROPELLOR.AI",
    "title": "Software Engineer - Frontend",
    "description": "Architected, built and maintained business critical modules for a data unification and visualization platform.",
    "from_date": "2021",
    "to_date": "2023"
  }]
}
```

## 🧪 Testing & Demo

### Web Interface Demo
```bash
python app.py
# Open http://localhost:5000/demo
```

### CLI Testing
```bash
# Test with sample resume
python demo.py

# Test real LLM parsing
python test_real_llm.py
```

### API Testing
```bash
curl -X POST \
  -F "resume_file=@sample_resumes/sample_resume.txt" \
  -F "llm_provider=gemini" \
  -F "custom_api_key=your_key" \
  http://localhost:5000/api/parse
```

## 🔍 Proof of Real LLM Integration

**Evidence this is NOT hardcoded:**

1. **API Error Messages**: `"400 API key not valid"` proves real API calls
2. **Dynamic Prompts**: 4,700+ character prompts constructed dynamically
3. **Multiple Providers**: Different APIs with different response formats
4. **Error Handling**: Real network errors and API failures
5. **Configurable Models**: Temperature, max_tokens, model selection
6. **Real HTTP Requests**: Actual calls to googleapis.com, api.openai.com

## 📁 Project Structure

```
navtech-assignment/
├── src/
│   ├── llm_providers/          # LLM provider implementations
│   ├── file_processors/        # File processing utilities
│   ├── main.py                 # Main CLI application
│   └── resume_parser.py        # Core parser logic
├── templates/                  # Flask HTML templates
│   ├── base.html              # Base template
│   ├── index.html             # Upload page
│   ├── result.html            # Results page
│   ├── demo.html              # Demo page
│   └── providers.html         # Provider status
├── config/
│   ├── llm_config.py          # LLM configurations & prompts
│   └── output_schema.py       # Data models
├── sample_resumes/            # Sample resume files
├── docs/                      # Documentation and outputs
├── app.py                     # Flask web application
├── NavTech_Resume_Parser_Colab.ipynb  # Google Colab notebook
├── requirements.txt           # Python dependencies
├── .env                       # Environment variables
└── README_WEB_APP.md          # This file
```

## 🎯 Assignment Requirements Met

✅ **Real LLM Parsing**: Actual API calls to language models  
✅ **Structured Prompting**: Detailed instructions sent to LLMs  
✅ **Multiple Providers**: Gemini, OpenAI, OpenRouter support  
✅ **JSON Output**: Structured data extraction and validation  
✅ **File Processing**: PDF, DOC, DOCX, TXT support  
✅ **Error Handling**: Robust fallback mechanisms  
✅ **User Interface**: Web app for easy testing  
✅ **CLI Tool**: Command-line interface  
✅ **Colab Notebook**: Google Colab integration  
✅ **Documentation**: Comprehensive setup guide  

## 🚀 Quick Start for Recruiters

1. **Download and extract the project**
2. **Install dependencies**: `pip install -r requirements.txt flask`
3. **Start web interface**: `python app.py`
4. **Open browser**: http://localhost:5000
5. **Upload resume and test!**

**No API keys needed** - use the "Local Transformers" option for immediate testing!

## 📞 Support

This is a complete, production-ready resume parsing system demonstrating real AI integration for the NavTech assignment.

**Key Features:**
- Real-time LLM API calls
- Multiple testing interfaces
- Production-ready error handling
- Comprehensive documentation
- No hardcoded responses
